{% extends "base.html" %}

{% block title %}Configuration{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Configuration Settings
                    </h3>
                </div>
                <div class="card-body">
                    {% if success %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>Configuration updated successfully!
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endif %}
                    
                    {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endif %}

                    <form method="POST">
                        <!-- Discord Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fab fa-discord me-2"></i>Discord Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="raider_role_id" class="form-label">Raider Role ID</label>
                                            <input type="text" class="form-control" id="raider_role_id" name="raider_role_id" 
                                                   value="{{ config.discord.raider_role_id }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="guild_id" class="form-label">Discord Server ID</label>
                                            <input type="text" class="form-control" id="guild_id" name="guild_id" 
                                                   value="{{ config.discord.guild_id }}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="support_role_id" class="form-label">Support Role ID</label>
                                            <input type="text" class="form-control" id="support_role_id" name="support_role_id"
                                                   value="{{ config.discord.support_role_id }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ticket_category_id" class="form-label">Ticket Category ID</label>
                                            <input type="text" class="form-control" id="ticket_category_id" name="ticket_category_id"
                                                   value="{{ config.discord.ticket_category_id }}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ticket_cooldown" class="form-label">Ticket Cooldown (seconds)</label>
                                            <input type="number" class="form-control" id="ticket_cooldown" name="ticket_cooldown"
                                                   value="{{ config.game_settings.ticket_cooldown }}" required min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_tickets_per_user" class="form-label">Max Tickets Per User</label>
                                            <input type="number" class="form-control" id="max_tickets_per_user" name="max_tickets_per_user"
                                                   value="{{ config.game_settings.max_tickets_per_user }}" required min="1" max="10">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Warcraft Logs Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Warcraft Logs Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="wl_client_id" class="form-label">Client ID</label>
                                            <input type="text" class="form-control" id="wl_client_id" name="wl_client_id" 
                                                   value="{{ config.warcraft_logs.client_id }}" required>
                                            <div class="form-text">Warcraft Logs API Client ID</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="wl_client_secret" class="form-label">Client Secret</label>
                                            <input type="password" class="form-control" id="wl_client_secret" name="wl_client_secret" 
                                                   value="{{ config.warcraft_logs.client_secret }}" required>
                                            <div class="form-text">Warcraft Logs API Client Secret</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="wl_guild_id" class="form-label">Guild ID</label>
                                            <input type="number" class="form-control" id="wl_guild_id" name="wl_guild_id" 
                                                   value="{{ config.warcraft_logs.guild_id }}" required>
                                            <div class="form-text">Warcraft Logs Guild ID</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Game Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>Game Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="tier_ilvl" class="form-label">Tier Item Level</label>
                                            <input type="number" class="form-control" id="tier_ilvl" name="tier_ilvl"
                                                   value="{{ config.game_settings.tier_ilvl }}" required min="1" max="1000">
                                            <div class="form-text">Minimum item level for tier pieces</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="weathered_crest_id" class="form-label">Weathered Crest ID</label>
                                            <input type="number" class="form-control" id="weathered_crest_id" name="weathered_crest_id"
                                                   value="{{ config.game_settings.weathered_crest_id }}" required min="1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="carved_crest_id" class="form-label">Carved Crest ID</label>
                                            <input type="number" class="form-control" id="carved_crest_id" name="carved_crest_id"
                                                   value="{{ config.game_settings.carved_crest_id }}" required min="1">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="runed_crest_id" class="form-label">Runed Crest ID</label>
                                            <input type="number" class="form-control" id="runed_crest_id" name="runed_crest_id"
                                                   value="{{ config.game_settings.runed_crest_id }}" required min="1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gilded_crest_id" class="form-label">Gilded Crest ID</label>
                                            <input type="number" class="form-control" id="gilded_crest_id" name="gilded_crest_id"
                                                   value="{{ config.game_settings.gilded_crest_id }}" required min="1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- App Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-server me-2"></i>Application Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="app_password" class="form-label">App Password</label>
                                            <input type="password" class="form-control" id="app_password" name="app_password" 
                                                   value="{{ config.app_settings.password }}" required>
                                            <div class="form-text">Password for accessing protected pages</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="host" class="form-label">Host</label>
                                            <input type="text" class="form-control" id="host" name="host" 
                                                   value="{{ config.app_settings.host }}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="port" class="form-label">Port</label>
                                            <input type="number" class="form-control" id="port" name="port" 
                                                   value="{{ config.app_settings.port }}" required min="1" max="65535">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="debug" name="debug" 
                                                       {% if config.app_settings.debug %}checked{% endif %}>
                                                <label class="form-check-label" for="debug">
                                                    Debug Mode
                                                </label>
                                                <div class="form-text">Enable debug mode for development</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Home
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
