# Configuration Management System

This document describes the password-protected configuration management system for the bot and scripts.

## Overview

The configuration system centralizes all configurable variables in a single `config.json` file and provides a web-based interface for managing these settings. All scripts (Python and PowerShell) read from this central configuration file.

## Configuration File

The `config.json` file contains the following sections:

**🔄 Dynamic Configuration**: All applications now read configuration values dynamically, meaning changes take effect immediately without requiring restarts!

### Discord Settings
- `raider_role_id`: Discord role ID for raiders
- `guild_id`: Discord server/guild ID  
- `support_role_id`: Discord role ID for support staff
- `ticket_category_id`: Discord category ID for tickets
- `target_server_id`: Target Discord server ID

### Warcraft Logs Settings
- `client_id`: Warcraft Logs API Client ID
- `client_secret`: Warcraft Logs API Client Secret
- `guild_id`: Warcraft Logs Guild ID

### Game Settings
- `tier_ilvl`: Minimum item level for tier pieces (used in Main.ps1)
- `ticket_cooldown`: Cooldown between ticket creation (seconds)
- `max_tickets_per_user`: Maximum active tickets per user

### Application Settings
- `password`: Password for accessing protected web pages
- `host`: Server host address
- `port`: Server port number
- `debug`: Enable/disable debug mode

## Web Configuration Interface

### Accessing the Config Page

1. Navigate to `http://your-server:port/config`
2. You will be redirected to the login page if not authenticated
3. Enter the password (default: "uproar321")
4. Access the configuration page

### Using the Config Page

- The configuration page is organized into sections matching the JSON structure
- All fields are validated (required fields, number ranges, etc.)
- Changes are saved immediately when the form is submitted
- Success/error messages are displayed after save attempts

## Files Modified

### Python Files
- `app.py`: Updated to use configuration for Discord settings, passwords, and server settings (dynamic)
- `discord_bot.py`: Updated to use dynamic configuration functions for Discord IDs and settings
- `Discord_bot_ticketSystem.py`: Updated to use dynamic configuration functions for ticket system settings

### PowerShell Files
- `PS_version/Main.ps1`: Updated to use configuration for tier_ilvl and Warcraft Logs settings
- `PS_version/get_logs.ps1`: Updated to use configuration for Warcraft Logs API settings

### Templates
- `templates/config.html`: New configuration page template
- `templates/base.html`: Updated navigation to include config link

## Configuration Loading

### Python (Dynamic)
```python
# Flask App - Reloads on each request
def load_config():
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return default_config

# Discord Bots - Helper functions that reload config each time
def get_support_role_id():
    config = load_config()
    return int(config.get('discord', {}).get('support_role_id', '1060628081359982714'))
```

### PowerShell
```powershell
function Get-Config {
    try {
        $configPath = "..\config.json"
        if (Test-Path $configPath) {
            $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
            return $configContent
        }
    } catch {
        Write-Warning "Error loading config file: $_"
        return $null
    }
}
```

## Security

- The configuration page is password-protected using the same authentication system as other protected pages
- The password itself can be changed through the configuration interface
- Configuration changes take effect immediately for new requests/script runs

## Backup and Recovery

- Always backup `config.json` before making manual changes
- The web interface validates all inputs before saving
- Invalid configurations will show error messages and not be saved

## Default Values

If the configuration file is missing or corrupted, the system falls back to hardcoded default values that match the original settings in the codebase.

## Dynamic Configuration Behavior

### Immediate Effect Applications:
- **Flask Web App**: ✅ Changes take effect immediately (reloads config on each request)
- **Discord Bots**: ✅ Changes take effect immediately (dynamic config functions)
- **PowerShell Scripts**: ✅ Changes take effect on next script run

### No Restart Required!
All applications now read configuration dynamically, so changes made through the web interface take effect immediately without requiring any manual restarts.

## Testing

The system has been tested to ensure:
- ✅ Configuration loading works in both Python and PowerShell
- ✅ Configuration saving works through the web interface
- ✅ Updated values are immediately available to all applications
- ✅ Discord bots read configuration dynamically without restart
- ✅ Password protection functions correctly
- ✅ Fallback to defaults works when config is missing
